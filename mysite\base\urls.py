#!/usr/bin/env python
#coding=utf-8
from django.urls import re_path as url
#from django.contrib.auth.decorators import permission_required
from django.conf import settings
from mysite.iclock import dataview,export,ad_view
from mysite.base import sysview,views
from mysite.base import transferdata
from mysite.base import upgrade_views
from mysite.base.export_tasks import auto_export_2
import sys
# from mysite.base import register_view
from mysite.base.data_visualization_views import get_data_visualization, base_visualization

urlpatterns = [
##数据管理相关
     url(r'^data/(?P<ModelName>[^/]*)/$', dataview.DataList),
    url(r'^export/(?P<ModelName>[^/]*)/$', export.exportList),
    url(r'^data/(?P<ModelName>[^/]*)/_clear_/$', dataview.DataClear),
    url(r'^data/(?P<ModelName>[^/]*)/_del_old_/$', dataview.DataDelOld),
    url(r'^data/(?P<ModelName>[^/]*)/_new_/$', dataview.DataNew),
    url(r'^data/(?P<ModelName>[^/]*)/(?P<DataKey>[^/]*)/$', dataview.DataDetail),
    url(r'^isys/(?P<ModelName>[^/]*)/$', sysview.sysList),
 
    #软件授权用到的接口
    # url(r'^get_regional_data/', register_view.get_regional_data),
    # url(r'^do_active_licence', register_view.do_active_licence),
    # url(r'^do_import_licence', register_view.do_import_licence),
    # url(r'^download_upk_file', register_view.download_upk_file),
    # url(r'^do_update_licence', register_view.do_update_licence),
    # url(r'^do_check_cafile', register_view.do_check_cafile),
    # url(r'^download_file', views.download_file),
    # 导出许可
    url(r'verify_and_get_manage_license_url/$', views.verify_and_get_manage_license_url),
    url(r'manage_license/$', views.manage_license),

    #问题反馈
    url(r'^submit_question', views.submit_question),  #提交问题反馈

    
    url(r'^get_version_info/$',upgrade_views.get_version_info),  #在线升级，获取版本信息，执行于版本控制服务器
    
    url(r'^language/$',views.language),

    # 在线用户
    url(r'^show_onlineusers', views.show_onlineusers),  #在线用户列表

    #数据库备份记录相关
    url(r'^show_db_backup/$', views.show_db_backup),
    url(r'^delete_db_backup/$', views.delete_db_backup),

    #找回密码
    url(r'^forget_password/$',views.forget_password),
    url(r'^send_verification_code/$',views.send_verification_code),
    url(r'^modify_password/$',views.modify_password),
    url(r'^get_verification_image/$',views.get_verification_image),

    url(r'^show_history/$',views.show_history),
    url(r'^saveProviderInfo/$',views.saveProviderInfo),
    url(r'^showProviderInfo/$', views.showProviderInfo),

    url(r'^savePhoto/$',views.savePhoto),  # 上传人员照片
    url(r'^saveTemperature/$',views.saveTemperature),  # 上传人员体温
    url(r'^saveAllowPopup/$',views.saveAllowPopup),
    url(r'^bioWeb_systemReg/$',views.bioWeb_systemReg),
    url(r'^auto_export_task/$', views.auto_export_task),
    url(r'export_tasks/$', auto_export_2),  #自动导出调试开关
    url(r'test_adconn/$', ad_view.test_adconn),
    url(r'sync_ad_data/$', ad_view.sync_ad_data),
    url(r'base_monitor_/$', get_data_visualization),
    url(r'base_visualization/(?P<login_tag>[0,1]?)$', base_visualization),
    url(r'is_export_encript/$', views.is_export_encript),

]

#演示公网关于客户反馈相关功能路由
if settings.DEMO == 1:  
    urlpatterns += [
        url(r'^questions', views.save_feedback),  #保存问题反馈
        url(r'^show_questions', views.show_question),  #问题列表
        url(r'^show_unreplied_detail', views.show_unreplied_detail),  #反馈详情
        url(r'^question_replied', views.question_replied),  #回复
        url(r'^question_ignore', views.question_ignore),  #忽略/删除
        url(r'^get_maintenance_announcement', views.get_maintenance_announcement)  # 获取维护公告
    ]
if settings.SUPPORT_ONLINE_UPGRADE:
    urlpatterns += [
        #在线升级
        url(r'^check_updates/$',upgrade_views.check_updates),
        url(r'^execute_update/$',upgrade_views.execute_update)
    ]
if settings.DATABASE_UPGRADE:
    urlpatterns += [
        #迁移数据
        url(r'^database_upgrade/', transferdata.database_upgrade),
        url(r'^upgrade_progress/', transferdata.upgrade_progress)
    ]

# 多租户
if settings.MULTI_TENANT:
    from mysite.base import multitenant_views
    urlpatterns += [
        url(r'^multitenant/create_tenant/$', multitenant_views.create_tenant),  #创建租户
        url(r'^multitenant/change_tenant_information/$', multitenant_views.change_tenant_information),  #修改租信息
        url(r'^multitenant/del_tenant/$', multitenant_views.del_tenant),
        url(r'^multitenant/sync_qywx/$', multitenant_views.sync_qywx),  #同步企业微信（组织架构、人员信息）
        url(r'^multitenant/sync_compairson_infomation/$', multitenant_views.sync_compairson_infomation),  #比对信息）
        url(r'^multitenant/disable_tenant/$', multitenant_views.disable_tenant),  # 禁用/启用租户）
        url(r'^multitenant/add_auth_device/$', multitenant_views.add_auth_device),  # 新增授权设备）
        url(r'^multitenant/import_devices/$', multitenant_views.import_devices),  # 导入授权设备）
        url(r'^multitenant/del_auth_device/$', multitenant_views.del_auth_device),  # 删除授权设备）
        url(r'^multitenant/update_max_user_capacity/$', multitenant_views.update_max_user_capacity),  # 修改人员上限）
        url(r'^multitenant/update_max_device_capacity/$', multitenant_views.update_max_device_capacity),  # 修改设备上限）
        url(r'^multitenant/update_paid_until/$', multitenant_views.update_paid_until),  # 修改授权期限）
        url(r'^multitenant/update_auth_mods/$', multitenant_views.update_auth_mods),  # 修改授权模块）
        url(r'^multitenant/get_api_key/$', multitenant_views.get_api_key),  # 获取数据对接密钥）
    ]
